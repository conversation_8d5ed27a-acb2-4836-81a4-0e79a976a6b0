!function(e,o){"object"==typeof exports&&"undefined"!=typeof module?module.exports=o():"function"==typeof define&&define.amd?define(o):e.i18nextBrowserLanguageDetector=o()}(this,function(){"use strict";function e(e){return a.call(i.call(arguments,1),function(o){if(o)for(var t in o)void 0===e[t]&&(e[t]=o[t])}),e}function o(e,o){if(!(e instanceof o))throw new TypeError("Cannot call a class as a function")}function t(){return{order:["querystring","cookie","localStorage","navigator","htmlTag"],lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"]}}var n=[],a=n.forEach,i=n.slice,r={create:function(e,o,t,n){var a=void 0;if(t){var i=new Date;i.setTime(i.getTime()+60*t*1e3),a="; expires="+i.toGMTString()}else a="";n=n?"domain="+n+";":"",document.cookie=e+"="+o+a+";"+n+"path=/"},read:function(e){for(var o=e+"=",t=document.cookie.split(";"),n=0;n<t.length;n++){for(var a=t[n];" "===a.charAt(0);)a=a.substring(1,a.length);if(0===a.indexOf(o))return a.substring(o.length,a.length)}return null},remove:function(e){this.create(e,"",-1)}},u={name:"cookie",lookup:function(e){var o=void 0;if(e.lookupCookie&&"undefined"!=typeof document){var t=r.read(e.lookupCookie);t&&(o=t)}return o},cacheUserLanguage:function(e,o){o.lookupCookie&&"undefined"!=typeof document&&r.create(o.lookupCookie,e,o.cookieMinutes,o.cookieDomain)}},c={name:"querystring",lookup:function(e){var o=void 0;if("undefined"!=typeof window)for(var t=window.location.search.substring(1),n=t.split("&"),a=0;a<n.length;a++){var i=n[a].indexOf("=");if(i>0){var r=n[a].substring(0,i);r===e.lookupQuerystring&&(o=n[a].substring(i+1))}}return o}},l=void 0;try{l="undefined"!==window&&null!==window.localStorage;window.localStorage.setItem("i18next.translate.boo","foo"),window.localStorage.removeItem("i18next.translate.boo")}catch(e){l=!1}var s={name:"localStorage",lookup:function(e){var o=void 0;if(e.lookupLocalStorage&&l){var t=window.localStorage.getItem(e.lookupLocalStorage);t&&(o=t)}return o},cacheUserLanguage:function(e,o){o.lookupLocalStorage&&l&&window.localStorage.setItem(o.lookupLocalStorage,e)}},d={name:"navigator",lookup:function(e){var o=[];if("undefined"!=typeof navigator){if(navigator.languages)for(var t=0;t<navigator.languages.length;t++)o.push(navigator.languages[t]);navigator.userLanguage&&o.push(navigator.userLanguage),navigator.language&&o.push(navigator.language)}return o.length>0?o:void 0}},f={name:"htmlTag",lookup:function(e){var o=void 0,t=e.htmlTag||("undefined"!=typeof document?document.documentElement:null);return t&&"function"==typeof t.getAttribute&&(o=t.getAttribute("lang")),o}},g={name:"path",lookup:function(e){var o=void 0;if("undefined"!=typeof window){var t=window.location.pathname.match(/\/([a-zA-Z-]*)/g);if(t instanceof Array)if("number"==typeof e.lookupFromPathIndex){if("string"!=typeof t[e.lookupFromPathIndex])return;o=t[e.lookupFromPathIndex].replace("/","")}else o=t[0].replace("/","")}return o}},p={name:"subdomain",lookup:function(e){var o=void 0;if("undefined"!=typeof window){var t=window.location.href.match(/(?:http[s]*\:\/\/)*(.*?)\.(?=[^\/]*\..{2,5})/gi);t instanceof Array&&(o="number"==typeof e.lookupFromSubdomainIndex?t[e.lookupFromSubdomainIndex].replace("http://","").replace("https://","").replace(".",""):t[0].replace("http://","").replace("https://","").replace(".",""))}return o}},h=function(){function e(e,o){for(var t=0;t<o.length;t++){var n=o[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(o,t,n){return t&&e(o.prototype,t),n&&e(o,n),o}}(),v=function(){function n(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};o(this,n),this.type="languageDetector",this.detectors={},this.init(e,t)}return h(n,[{key:"init",value:function(o){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.services=o,this.options=e(n,this.options||{},t()),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=a,this.addDetector(u),this.addDetector(c),this.addDetector(s),this.addDetector(d),this.addDetector(f),this.addDetector(g),this.addDetector(p)}},{key:"addDetector",value:function(e){this.detectors[e.name]=e}},{key:"detect",value:function(e){var o=this;e||(e=this.options.order);var t=[];e.forEach(function(e){if(o.detectors[e]){var n=o.detectors[e].lookup(o.options);n&&"string"==typeof n&&(n=[n]),n&&(t=t.concat(n))}});var n=void 0;if(t.forEach(function(e){if(!n){var t=o.services.languageUtils.formatLanguageCode(e);o.services.languageUtils.isWhitelisted(t)&&(n=t)}}),!n){var a=this.i18nOptions.fallbackLng;"string"==typeof a&&(a=[a]),a||(a=[]),n="[object Array]"===Object.prototype.toString.apply(a)?a[0]:a[0]||a.default&&a.default[0]}return n}},{key:"cacheUserLanguage",value:function(e,o){var t=this;o||(o=this.options.caches),o&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(e)>-1||o.forEach(function(o){t.detectors[o]&&t.detectors[o].cacheUserLanguage(e,t.options)}))}}]),n}();return v.type="languageDetector",v});
