<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg id="svg378" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="480" width="640" version="1" y="0" x="0" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata3305">
  <rdf:RDF>
   <cc:Work rdf:about="">
  <dc:format>image/svg+xml</dc:format>
  <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs id="defs380">
  <clipPath id="clipPath8528" clipPathUnits="userSpaceOnUse">
   <rect id="rect8530" fill-opacity="0.67" height="512" width="682.67" y=".0000024116" x="-85.334"/>
  </clipPath>
 </defs>
 <g id="flag" clip-path="url(#clipPath8528)" fill-rule="evenodd" transform="matrix(.9375 0 0 .9375 80.001 -.0000022609)">
  <rect id="rect149" height="512" width="768" y=".0000024116" x="-128" stroke-width="1pt" fill="#40a6ff"/>
  <path id="path205" d="m336.48 381.19-82.505-53.476-82.101 54.001 30.535-87.754-81.95-54.188 101.39-0.7555 31.447-87.488 32.121 87.286 101.39 0.11512-81.53 54.699 31.209 87.56z" stroke-width=".11287" fill="#fff"/>
 </g>
</svg>
